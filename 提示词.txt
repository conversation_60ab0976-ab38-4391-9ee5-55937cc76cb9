请根据“架构网络拓扑图（单中心）V4.html”架构设计一个prompt供AI调用，其中前端写到frontend，后端写到backend文件夹里面，写入到ReadMe.md

此项目分为以下模块
	后端backend
		1、bms-api 负责对外提供服务，校验输入参数，并且和 bms-biz 模块进行交互
		2、bms-biz 负责业务逻辑的处理，报文转换等逻辑在这里处理，和 bms-router 模块进行交互
		3、bms-router 负责请求其他系统
		4、bms-manage 后台管理页面对应的接口，需要登录后才可以使用，和 bms-biz、bms-batch 模块进行交互
		5、bms-batch 批量模块
		6、bms-common 后端的公共模块，比如公告的类、接口、方法等
	前端frontend
		1、bms-page 后台管理模块的页面，请求 bms-manage 模块提供的接口


首先在 ReadMe.md 开发计划与进度 拆分功能模块，格式如下所示，待我确认之后再进行下一步

- [√] 渠道管理基础功能
  - [√] 渠道实体定义与数据访问
  - [√] 渠道创建、更新和查询接口
  - [√] 渠道状态管理（启用/禁用）
- [ ] 监控与统计
  - [ ] Prometheus与Grafana集成
    - [ ] 自定义指标收集
    - [ ] 告警规则配置
    - [ ] 仪表板设计


我已经记录到 ReadMe.md 里面去了，请新建这5个模块的基本文件夹结构，注意4个后端代码单独放在一个文件夹里面，带我确认后进行下一个步骤

cd backend/bms-manage && mvn spring-boot:run



用serena 分析这个项目的执行流程


context7 用法
	直接在提示词后面跟上 use context7 ,如下所示
	Configure a Cloudflare Worker script to cache JSON API responses for five minutes. use context7


	全局里面写入。因为context7连接拒绝，全局规则的配置先放到这里了
	[[calls]]
	match = "when the user requests code examples, setup or configuration steps, or library/API documentation"
	tool  = "context7"



此项目是一个总体的架构框架，你用html+css+js先画原型，放在 doc\原型-架构后台管理系统 此目录下面，待我确认后进行下一步

我更新了ReadMe，新增了权限的管理方式为RBAC，你继续完善原型部分

系统配置管理 以及后面的原型参考 用户权限管理 这样的表格设计，未设计完成的继续完善 

首页
业务功能管理
API管理    汇总信息横向排列。首页
业务功能管理
外部系统  以及后面的原型还没设计完成，请完善，直到MVP版本


你没完成，所有“功能开发中”的全部完善

这种竖着的汇总信息改为横向布局，类似“首页
业务功能管理
业务逻辑”这里的汇总，需要改动的页面有  首页
业务功能管理
API管理、首页
批量任务管理
任务日志、首页
监控与报警
性能监控、首页
监控与报警
报警管理、首页
监控与报警
指标管理、首页
日志管理
系统日志、首页
日志管理
操作日志





