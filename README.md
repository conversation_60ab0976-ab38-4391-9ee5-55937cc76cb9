# 后台管理系统 (Backend Management System)

## 项目概述
本项目是一个基于微服务架构的后台管理系统，采用单中心架构设计，前后端分离开发模式。系统具有高可用性、高可扩展性和高性能，涵盖了从服务注册与发现、流量控制、熔断、配置管理、API网关到监控和日志管理的各个环节。

## 架构设计
系统基于"架构网络拓扑图（单中心）V4"设计，主要组件包括：

### 前端 (Frontend)
- 基于Vue3框架开发
- 使用soybean-admin作为UI框架
- 功能特点：
  - 多线程分片/断点下载大文件支持
  - WebSocket(或SseEmitter)协议实现服务端主动向客户端推送数据

### 后端 (Backend)
- 基于SpringBoot + JDK8开发
- 应用服务器：Tomcat
- 微服务架构：
  - bms-api 对外提供服务接口
  - bms-biz 业务逻辑处理
  - bms-router 负责请求其他系统
  - bms-manage 后台管理接口
  - bms-batch 批量处理模块
  - bms-common 公共模块
- 数据层：
  - MySQL 5.7 (分库分表)
  - Redis集群 (缓存、分布式锁、防重复提交)
  - TDSql数据库集群
- 服务治理：
  - Nacos (服务注册发现、配置管理)
  - RocketMQ和Kafka (消息队列)
  - Canal (数据同步)
  - Databus (ETL工具)
- 监控与日志：
  - SkyWalking (分布式链路跟踪)
  - Prometheus (性能监控)
  - Log4j2 (异步日志)
- 网关层：
  - Nginx/OpenResty (负载均衡、静态资源)
  - SpringCloudGateway (API网关)
- 权限管理：
  -RBAC（基于角色的访问控制）

## 技术栈
- 前端：
  - HTML + CSS 
  - Vue3
  - Soybean-Admin
- 后端：
  - JDK8
  - SpringBoot
  - MyBatis
  - MySQL 5.7
  - Redis
  - Nacos
  - RocketMQ
  - Kafka
  - Spring Cloud Stream
  - Feign Client
  - TDSql
  - Canal
  - Databus

## 功能模块
1. 用户权限管理
2. 系统配置管理
3. 业务功能管理
4. 批量任务管理
5. 监控与报警
6. 日志管理
7. 系统集成接口

## 项目模块结构
### 后端 (Backend)
1. **bms-api** - 对外提供服务，校验输入参数，与bms-biz模块交互
2. **bms-biz** - 业务逻辑处理，报文转换，与bms-router模块交互
3. **bms-router** - 负责请求其他系统
4. **bms-manage** - 后台管理页面接口，需要登录，与bms-biz、bms-batch模块交互
5. **bms-batch** - 批量处理模块
6. **bms-common** - 后端公共模块，包含共享的类、接口、方法等

### 前端 (Frontend)
1. **bms-page** - 后台管理模块页面，请求bms-manage模块提供的接口

## 开发环境要求
- JDK 8
- Maven 3.6+
- Node.js 16+
- MySQL 5.7
- Redis 6+

## 开发规范
- 代码结构清晰，遵循最佳实践
- 前端代码放置于frontend/bms-page目录
- 后端代码放置于backend目录下对应模块
- 统一异常处理
- API接口文档规范
- 日志记录规范
- 单元测试覆盖率要求

## 开发计划与进度
- [ ] 项目基础架构搭建
  - [ ] 后端基础架构
    - [ ] bms-common公共模块开发
    - [ ] 统一异常处理机制
    - [ ] 统一响应格式
    - [ ] 日志框架配置
  - [ ] 前端基础架构
    - [ ] soybean-admin框架集成
    - [ ] 路由配置
    - [ ] 状态管理
    - [ ] 通用组件库

- [ ] 用户权限管理
  - [ ] 用户管理
    - [ ] 用户实体定义与数据访问
    - [ ] 用户CRUD接口
    - [ ] 用户状态管理
  - [ ] 角色管理
    - [ ] 角色实体定义与数据访问
    - [ ] 角色CRUD接口
  - [ ] 权限管理
    - [ ] 权限模型设计
    - [ ] 权限分配接口
    - [ ] 权限验证机制

- [ ] 系统配置管理
  - [ ] 系统参数配置
    - [ ] 配置实体定义与数据访问
    - [ ] 配置CRUD接口
    - [ ] 配置缓存机制
  - [ ] 字典管理
    - [ ] 字典实体定义与数据访问
    - [ ] 字典CRUD接口
    - [ ] 字典缓存机制

- [ ] 业务功能管理
  - [ ] bms-api模块开发
    - [ ] API接口定义
    - [ ] 参数校验
    - [ ] 接口安全控制
  - [ ] bms-biz模块开发
    - [ ] 业务逻辑实现
    - [ ] 报文转换功能
  - [ ] bms-router模块开发
    - [ ] 外部系统接口定义
    - [ ] 请求路由逻辑
    - [ ] 错误处理机制

- [ ] 批量任务管理
  - [ ] bms-batch模块开发
    - [ ] 批量任务定义
    - [ ] 任务调度机制
    - [ ] 任务执行监控
  - [ ] 批量任务界面
    - [ ] 任务配置界面
    - [ ] 任务监控界面
    - [ ] 任务日志查看

- [ ] 后台管理功能
  - [ ] bms-manage模块开发
    - [ ] 管理接口定义
    - [ ] 管理业务逻辑
    - [ ] 权限控制
  - [ ] bms-page页面开发
    - [ ] 登录页面
    - [ ] 仪表板页面
    - [ ] 系统管理页面
    - [ ] 业务管理页面

- [ ] 监控与报警
  - [ ] Prometheus与Grafana集成
    - [ ] 自定义指标收集
    - [ ] 告警规则配置
    - [ ] 仪表板设计
  - [ ] 日志管理
    - [ ] 日志收集配置
    - [ ] 日志查询界面
    - [ ] 日志分析功能

- [ ] 系统集成
  - [ ] 外部系统接口集成
  - [ ] 数据同步机制
  - [ ] 消息队列集成

- [ ] 性能优化与测试
  - [ ] 性能测试
  - [ ] 安全测试
  - [ ] 功能测试
  - [ ] 接口测试

- [ ] 部署与上线
  - [ ] Docker镜像构建
  - [ ] K8s部署配置
  - [ ] 持续集成设置
  - [ ] 运维文档编写

## 部署架构
系统采用容器化部署方案，使用Docker+K8s进行集群管理，确保系统的高可用性与可扩展性。

## 系统安全
- 数字证书/数字信封-获取SM4秘钥
- API接口安全认证
- 数据加密传输
- 敏感数据脱敏
- 防SQL注入
- 防XSS攻击
- IP黑白名单控制 